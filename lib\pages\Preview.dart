import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/Navigator.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/CloudScenario.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/NewScenarioWithPlayer.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/components/PlayMenu.dart';
import 'package:simsushare_player/utils/constants.dart';

class Preview extends StatefulWidget {
  final String? url;
  Preview({
    Key? key,
    this.url,
  }) : super(key: key);

  @override
  State<Preview> createState() => _PreviewState();
}

class _PreviewState extends State<Preview> {
  final args = Get.arguments;

  Rx<List<CloudScenario>> scenarios = Rx<List<CloudScenario>>([]);
  bool inEditor = false;

  SimPlayer sp = SimPlayer(playMode: true);
  final _simController = Get.find<SimController>();
  late Scenario sim;

  // Multi-location grid view state
  final isMultiLocationMode = false.obs;
  final List<SimPlayer> multiLocationPlayers = [];
  final List<SimController> additionalControllers = [];
  final List<String> selectedLocationIds = [];
  int gridType = 2;

  // FlutterView theView = WidgetsBinding.instance.platformDispatcher.views.first;

  _initialize() async {
    if (widget.url != null) {
      UserController _userController = Get.find();
      if (_userController.user.value == null) {
        print("User value is null in user controller");
        return;
      }
      /* try { */
      // print(_userController.user.value);
      final response = await dio.get("/scenarios/" + _userController.user.value["company"]["_id"]);
      // print("${response.statusCode} ${response.data}");
      if (response.statusCode != 200) {
        return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
      }
      final scenariosData = response.data["scenarios"];
      print(scenariosData[0]);
      print(scenariosData[0].runtimeType);
      print(scenariosData[0].toString());
      scenarios.value = List<CloudScenario>.from(scenariosData.map((s) => CloudScenario.fromJson(s)), growable: true);
      // print("Scenarios: ${scenarios.value}");
      /* } catch (err) {
      print(err);
    } */
    }
    // if (_simController.currentSim.value == null) return;
    sim = _simController.currentSim.value!.copy();
    sim.inPlayMode = true;
    sim.masks = sim.masks.map((mask) {
      if (!mask.needsParsing()) {
        mask.scale(1 / sim.width, 1 / sim.height);
      }
      return mask;
    }).toList();

    if (Get.arguments?["inEditor"] == "true") {
      inEditor = true;
    }
    sp = SimPlayer(playMode: true, inEditor: inEditor, providedSim: sim);
  }

  void _showMultiLocationDialog() {
    showDialog(
      context: Get.context!,
      builder: (context) => MultiViewLocationSelector(
        availableLocations: sim.locations,
        onLocationsSelected: _startMultiLocationView,
      ),
    );
  }

  void _startMultiLocationView(List<String> locationIds, int selectedGridType) {
    selectedLocationIds.clear();
    selectedLocationIds.addAll(locationIds);
    gridType = selectedGridType;

    // Create additional SimController instances for each location
    _createAdditionalControllers(locationIds);

    isMultiLocationMode.value = true;
  }

  void _createAdditionalControllers(List<String> locationIds) {
    // Clear existing additional controllers
    _disposeAdditionalControllers();

    for (int i = 0; i < locationIds.length; i++) {
      final tag = 'multi_location_$i';
      final controller = SimController();

      // Initialize the controller with the same sim but set to specific location
      final locationIndex = sim.locations.indexWhere((loc) => loc.id == locationIds[i]);
      controller.currentSim.value = sim.copy(clone: true);
      controller.currentLocation.value = locationIndex;
      controller.currentState.value = _simController.currentState.value;

      // Register the controller with GetX using the tag
      Get.put(controller, tag: tag);
      additionalControllers.add(controller);

      // Create SimPlayer for this location
      final player = SimPlayer(
        playMode: true,
        inEditor: inEditor,
        providedSim: sim,
        controllerTag: tag,
      );
      multiLocationPlayers.add(player);
    }
  }

  void _disposeAdditionalControllers() {
    for (int i = 0; i < additionalControllers.length; i++) {
      final tag = 'multi_location_$i';
      try {
        Get.delete<SimController>(tag: tag);
      } catch (e) {
        print('Error disposing controller with tag $tag: $e');
      }
    }
    additionalControllers.clear();
    multiLocationPlayers.clear();
  }

  void _exitMultiLocationMode() {
    _disposeAdditionalControllers();
    isMultiLocationMode.value = false;
    selectedLocationIds.clear();
  }

  @override
  void dispose() {
    // Ensure all additional controllers are disposed when leaving the preview
    _disposeAdditionalControllers();
    super.dispose();
  }

  Widget _buildMultiLocationGrid(double selectedWidth, double selectedHeight, double selectedScale) {
    if (multiLocationPlayers.isEmpty) {
      return const Center(
        child: Text(
          'Loading multi-location view...',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    final gridWidth = selectedWidth * selectedScale;
    final gridHeight = selectedHeight * selectedScale;

    if (gridType == 2) {
      // Side by side layout
      return Center(
        child: SizedBox(
          width: gridWidth,
          height: gridHeight,
          child: Column(
            children: [
              for (int i = 0; i < min(2, multiLocationPlayers.length); i++) ...[
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
                    ),
                    child: GameWidget(game: multiLocationPlayers[i]),
                  ),
                ),
                if (i < min(2, multiLocationPlayers.length) - 1) Container(width: 2, color: Colors.white.withOpacity(0.5)),
              ],
            ],
          ),
        ),
      );
    } else {
      // 2x2 grid layout
      return Center(
        child: SizedBox(
          width: gridWidth,
          height: gridHeight,
          child: Column(
            children: [
              // Top row
              Expanded(
                child: Row(
                  children: [
                    for (int i = 0; i < min(2, multiLocationPlayers.length); i++) ...[
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
                          ),
                          child: GameWidget(game: multiLocationPlayers[i]),
                        ),
                      ),
                      if (i < min(2, multiLocationPlayers.length) - 1) Container(width: 2, color: Colors.white.withOpacity(0.5)),
                    ],
                  ],
                ),
              ),
              // Divider
              if (multiLocationPlayers.length > 2) Container(height: 2, color: Colors.white.withOpacity(0.5)),
              // Bottom row
              if (multiLocationPlayers.length > 2)
                Expanded(
                  child: Row(
                    children: [
                      for (int i = 2; i < min(4, multiLocationPlayers.length); i++) ...[
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
                            ),
                            child: GameWidget(game: multiLocationPlayers[i]),
                          ),
                        ),
                        if (i < min(4, multiLocationPlayers.length) - 1) Container(width: 2, color: Colors.white.withOpacity(0.5)),
                      ],
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    }
  }

  double screenWidth = (kIsWeb && Get.context!.orientation == Orientation.landscape
      ? WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.height
      : WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width);
  // : Get.width);
  double screenHeight = (kIsWeb && Get.context!.orientation == Orientation.landscape
      ? WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width
      : WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.height);
  // : Get.height);

  @override
  Widget build(BuildContext context) {
    // final screenWidth = (kIsWeb ? theView.physicalSize.width : Get.width);
    // final screenHeight = (kIsWeb ? theView.physicalSize.height : Get.height);
    // print("isWeb: $kIsWeb, Screen width: $screenWidth, Screen height: $screenHeight");
    if (!kIsWeb) {
      if (Platform.isAndroid || Platform.isIOS) /* (breakpoint == Breakpoints.small) */ {
        SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
        final tempWidth = screenWidth;
        screenWidth = screenHeight;
        screenHeight = tempWidth;
      }
    }
    _initialize();
    // final selectedWidth = min(1200.0, sim.width);
    // final selectedHeight = min(800.0, sim.height);
    // final selectedWidth = min(1200.0, screenWidth);
    // final selectedHeight = min(800.0, screenHeight);
    // final selectedScale = min(1200 / selectedWidth, 800 / selectedHeight);
    // print("======= Preview ======== $selectedWidth x $selectedHeight @ (${1200 / selectedWidth} vs ${800 / selectedHeight}) $selectedScale");
    final selectedWidth = min(sim.width, screenWidth);
    final selectedHeight = min(sim.height, screenHeight);
    final selectedScale = min(sim.width / selectedWidth, sim.height / selectedHeight);
    print(
        "======= Preview ======== $selectedWidth x $selectedHeight @ (${sim.width / selectedWidth} vs ${sim.height / selectedHeight}) $selectedScale");
    print("SIM DIMENSIONS: ${sim.width} x ${sim.height}");
    return Scaffold(
      body: Container(
        // SafeArea(
        child: SizedBox.expand(
          child: KeyboardListener(
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  Obx(
                    () => isMultiLocationMode.value
                        ? _buildMultiLocationGrid(selectedWidth, selectedHeight, selectedScale)
                        : Center(
                            child: SizedBox(
                              width: selectedWidth * selectedScale,
                              height: selectedHeight * selectedScale,
                              child: Center(
                                child: GameWidget(game: sp),
                              ),
                            ),
                          ),
                  ),
                  Obx(
                    (() {
                      try {
                        final widthFactor = screenWidth / (_simController.currentSim.value?.width ?? 1);
                        final heightFactor = screenHeight / (_simController.currentSim.value?.height ?? 1);
                        print("Width factor: $widthFactor, Height factor: $heightFactor");
                        print("Screen width: $screenWidth, Screen height: $screenHeight");
                        print("Sim width: ${_simController.currentSim.value?.width}, Sim height: ${_simController.currentSim.value?.height}");
                        print("Calculation width: $screenWidth / ${_simController.currentSim.value?.width ?? 1}");
                        print("Calculation height: $screenHeight / ${_simController.currentSim.value?.height ?? 1}");
                        // NOTE: For some reason, a bias of 36 multiplied by the factor is required to get the correct position
                        return Positioned(
                          left: _simController.currentSim.value?.navClusterX != null
                              ? (_simController.currentSim.value!.navClusterX! * widthFactor) + (36 * widthFactor) /* bias */
                              : 20,
                          bottom: _simController.currentSim.value?.navClusterY != null
                              ? (_simController.currentSim.value!.navClusterY! * heightFactor) + (36 * heightFactor) /* bias */
                              : 20,
                          // height: isMobileScreen || isMobile ? 160 : screenHeight * 0.2,
                          // width: isMobileScreen || isMobile ? 160 : screenHeight * 0.2,
                          height: isMobileScreen || isMobile ? selectedHeight : screenHeight * 0.2,
                          width: isMobileScreen || isMobile ? selectedWidth : screenHeight * 0.2,
                          child: SimNavigator(locId: sim.locations[_simController.currentLocation.value].id, scenario: sim),
                        );
                      } catch (err, stacktrace) {
                        print("$err+${stacktrace.toString()}");
                        return Text(_simController.currentSim.value == null ? "No current sim" : "Error");
                      }
                    }),
                  ),
                  if (!kIsWeb)
                    Positioned(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: const BoxDecoration(
                          color: mainBackgrounds,
                          borderRadius: BorderRadius.only(bottomRight: Radius.circular(10)),
                        ),
                        child: IconButton(
                          onPressed: () {
                            _simController.currentSim.value!.inPlayMode = false;
                            _simController.currentSim.refresh();
                            if (inEditor) {
                              // Get.offAndToNamed("/create", parameters: );
                              // Get.offNamed("/create");
                              Get.off(NewScenarioWithPlayer(), duration: const Duration(milliseconds: 300));
                            } else {
                              Get.back();
                            }
                            // Get.offAllNamed("/home");
                            // Get.back();
                            /* Future.delayed(const Duration(milliseconds: 500), () {
                              _simController.currentSim.refresh();
                            }); */
                          },
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  Obx(
                    () => Positioned(
                      top: 10,
                      left: 70,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                          // border: Border.all(color: mainBackgrounds),
                        ),
                        constraints: const BoxConstraints(maxWidth: 150),
                        child: DropdownButton(
                          isExpanded: true,
                          isDense: true,
                          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
                          value: sim.locations[_simController.currentLocation.value].id,
                          onChanged: (value) {
                            _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == value);
                            _simController.currentSim.refresh();
                          },
                          underline: const SizedBox(),
                          style: const TextStyle(color: Colors.white, fontSize: 20),
                          dropdownColor: Colors.white,
                          borderRadius: const BorderRadius.all(Radius.circular(10)),
                          items: sim.locations
                              .map(
                                (loc) => DropdownMenuItem(
                                  value: loc.id,
                                  child: Text(
                                    "${loc.name} (${sim.states.firstWhere((state) => loc.state == state.id).name})",
                                    style: const TextStyle(color: Colors.black, fontSize: 14),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => Positioned(
                      top: 10,
                      left: 230,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                          // border: Border.all(color: mainBackgrounds),
                        ),
                        constraints: const BoxConstraints(maxWidth: 150),
                        child: DropdownButton(
                          isExpanded: true,
                          isDense: true,
                          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
                          value: sim.locations[_simController.currentLocation.value].state,
                          onChanged: (value) {
                            final targetState = sim.states.indexWhere((state) => state.id == value);
                            final targetLocation = sim.locations.firstWhereOrNull(
                                (loc) => loc.state == value && loc.name == sim.locations[_simController.currentLocation.value].name);
                            if (targetLocation != null) {
                              _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == targetLocation.id);
                            } else {
                              _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.state == value);
                            }
                            _simController.currentState.value = targetState;
                            _simController.currentSim.refresh();
                          },
                          underline: const SizedBox(),
                          style: const TextStyle(color: Colors.white, fontSize: 20),
                          dropdownColor: Colors.white,
                          borderRadius: const BorderRadius.all(Radius.circular(10)),
                          items: sim.states
                              .map(
                                (state) => DropdownMenuItem(
                                  value: state.id,
                                  child: Text(
                                    state.name,
                                    style: const TextStyle(color: Colors.black, fontSize: 14),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    ),
                  ),
                  // Multi-view button
                  Positioned(
                    top: 10,
                    right: 20,
                    child: Obx(() => Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isMultiLocationMode.value ? yellow : mainBackgrounds,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (isMultiLocationMode.value) ...[
                                IconButton(
                                  onPressed: _exitMultiLocationMode,
                                  icon: Icon(
                                    Icons.close,
                                    color: isMultiLocationMode.value ? Colors.black : Colors.white,
                                  ),
                                  tooltip: 'Exit Multi-View',
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Multi-View (${gridType})',
                                  style: TextStyle(
                                    color: isMultiLocationMode.value ? Colors.black : Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ] else ...[
                                IconButton(
                                  onPressed: _showMultiLocationDialog,
                                  icon: const Icon(
                                    Icons.grid_view,
                                    color: Colors.white,
                                  ),
                                  tooltip: 'Multi-Location View',
                                ),
                                const Text(
                                  'Multi-View',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        )),
                  ),
                  const HideNavigatorButton(),
                  PlayMenu(),
                ],
              ),
            ),
            onKeyEvent: (event) {
              final _simController = Get.find<SimController>();
              final sim = _simController.currentSim.value!;
              final currentLocation = sim.locations[_simController.currentLocation.value];
              int index = -1;
              switch (event.logicalKey.keyLabel.toLowerCase()) {
                case "w":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "N");
                  break;
                case "a":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "W");
                  break;
                case "d":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "E");
                  break;
                case "x":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "S");
                  break;
                case "q":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "NW");
                  break;
                case "e":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "NE");
                  break;
                case "z":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "SW");
                  break;
                case "c":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "SE");
                  break;
                default:
                  if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.arrowUp) {
                    index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "UP");
                    break;
                  }
                  if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.arrowDown) {
                    index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "DOWN");
                  }
              }
              if (index != -1) {
                _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == sim.navigations[index].to);
                _simController.currentSim.refresh();
              }
            },
            focusNode: FocusNode(),
            autofocus: true,
          ),
        ),
      ),
    );
  }
}

class MultiViewLocationSelector extends StatefulWidget {
  final List<SimulationLocation> availableLocations;
  final Function(List<String> locationIds, int gridType) onLocationsSelected;

  const MultiViewLocationSelector({
    Key? key,
    required this.availableLocations,
    required this.onLocationsSelected,
  }) : super(key: key);

  @override
  State<MultiViewLocationSelector> createState() => _MultiViewLocationSelectorState();
}

class _MultiViewLocationSelectorState extends State<MultiViewLocationSelector> {
  final _simController = Get.find<SimController>();
  int selectedGridType = 2;
  List<String> selectedLocationIds = [];

  @override
  void initState() {
    super.initState();
    // Pre-select current location if available
    if (_simController.currentSim.value != null && _simController.currentLocation.value >= 0) {
      final currentLoc = _simController.currentSim.value!.locations[_simController.currentLocation.value];
      if (widget.availableLocations.any((loc) => loc.id == currentLoc.id)) {
        selectedLocationIds.add(currentLoc.id);
      }
    }
  }

  void _toggleLocationSelection(String locationId) {
    setState(() {
      if (selectedLocationIds.contains(locationId)) {
        selectedLocationIds.remove(locationId);
      } else {
        if (selectedLocationIds.length < selectedGridType) {
          selectedLocationIds.add(locationId);
        }
      }
    });
  }

  void _onGridTypeChanged(int gridType) {
    setState(() {
      selectedGridType = gridType;
      // If we have more locations selected than the new grid type allows, trim the list
      if (selectedLocationIds.length > gridType) {
        selectedLocationIds = selectedLocationIds.take(gridType).toList();
      }
    });
  }

  bool _canProceed() {
    return selectedLocationIds.length == selectedGridType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: mainBackgrounds,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            const Text(
              'Multi-Location Preview',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Grid type selection
            const Text(
              'Select grid layout:',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildGridTypeOption(2, '2 Locations (Side by Side)'),
                const SizedBox(width: 16),
                _buildGridTypeOption(4, '4 Locations (2x2 Grid)'),
              ],
            ),
            const SizedBox(height: 20),

            // Location selection
            Text(
              'Select ${selectedGridType} locations (${selectedLocationIds.length}/${selectedGridType} selected):',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(height: 12),

            // Location list
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              child: SingleChildScrollView(
                child: Column(
                  children: widget.availableLocations.map((location) {
                    final isSelected = selectedLocationIds.contains(location.id);
                    final canSelect = selectedLocationIds.length < selectedGridType || isSelected;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: canSelect ? () => _toggleLocationSelection(location.id) : null,
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? yellow.withOpacity(0.2)
                                  : canSelect
                                      ? Colors.white.withOpacity(0.1)
                                      : Colors.grey.withOpacity(0.1),
                              border: Border.all(
                                color: isSelected ? yellow : Colors.white.withOpacity(0.3),
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                                  color: isSelected ? yellow : (canSelect ? Colors.white : Colors.grey),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        location.name,
                                        style: TextStyle(
                                          color: canSelect ? Colors.white : Colors.grey,
                                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        ),
                                      ),
                                      if (_simController.currentSim.value != null)
                                        Text(
                                          'State: ${_simController.currentSim.value!.states.firstWhereOrNull((state) => state.id == location.state)?.name ?? 'Unknown'}',
                                          style: TextStyle(
                                            color: canSelect ? Colors.white70 : Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Colors.white70),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _canProceed()
                      ? () {
                          widget.onLocationsSelected(selectedLocationIds, selectedGridType);
                          Get.back();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: yellow,
                    foregroundColor: Colors.black,
                  ),
                  child: const Text('Start Multi-View'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridTypeOption(int gridType, String label) {
    final isSelected = selectedGridType == gridType;
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onGridTypeChanged(gridType),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isSelected ? yellow.withOpacity(0.2) : Colors.white.withOpacity(0.1),
              border: Border.all(
                color: isSelected ? yellow : Colors.white.withOpacity(0.3),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  color: isSelected ? yellow : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
