import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class MultiViewLocationSelector extends StatefulWidget {
  final List<SimulationLocation> availableLocations;
  final Function(List<String> locationIds, int gridType) onLocationsSelected;

  const MultiViewLocationSelector({
    Key? key,
    required this.availableLocations,
    required this.onLocationsSelected,
  }) : super(key: key);

  @override
  State<MultiViewLocationSelector> createState() => _MultiViewLocationSelectorState();
}

class _MultiViewLocationSelectorState extends State<MultiViewLocationSelector> {
  final _simController = Get.find<SimController>();
  int selectedGridType = 2;
  List<String> selectedLocationIds = [];

  @override
  void initState() {
    super.initState();
    // Pre-select current location if available
    if (_simController.currentSim.value != null && _simController.currentLocation.value >= 0) {
      final currentLoc = _simController.currentSim.value!.locations[_simController.currentLocation.value];
      if (widget.availableLocations.any((loc) => loc.id == currentLoc.id)) {
        selectedLocationIds.add(currentLoc.id);
      }
    }
  }

  void _toggleLocationSelection(String locationId) {
    setState(() {
      if (selectedLocationIds.contains(locationId)) {
        selectedLocationIds.remove(locationId);
      } else {
        if (selectedLocationIds.length < selectedGridType) {
          selectedLocationIds.add(locationId);
        }
      }
    });
  }

  void _onGridTypeChanged(int gridType) {
    setState(() {
      selectedGridType = gridType;
      // If we have more locations selected than the new grid type allows, trim the list
      if (selectedLocationIds.length > gridType) {
        selectedLocationIds = selectedLocationIds.take(gridType).toList();
      }
    });
  }

  bool _canProceed() {
    return selectedLocationIds.length == selectedGridType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: mainBackgrounds,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            const Text(
              'Multi-Location Preview',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Grid type selection
            const Text(
              'Select grid layout:',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildGridTypeOption(2, '2 Locations (Side by Side)'),
                const SizedBox(width: 16),
                _buildGridTypeOption(4, '4 Locations (2x2 Grid)'),
              ],
            ),
            const SizedBox(height: 20),

            // Location selection
            Text(
              'Select ${selectedGridType} locations (${selectedLocationIds.length}/${selectedGridType} selected):',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(height: 12),

            // Location list
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              child: SingleChildScrollView(
                child: Column(
                  children: widget.availableLocations.map((location) {
                    final isSelected = selectedLocationIds.contains(location.id);
                    final canSelect = selectedLocationIds.length < selectedGridType || isSelected;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: canSelect ? () => _toggleLocationSelection(location.id) : null,
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? yellow.withOpacity(0.2)
                                  : canSelect
                                      ? Colors.white.withOpacity(0.1)
                                      : Colors.grey.withOpacity(0.1),
                              border: Border.all(
                                color: isSelected ? yellow : Colors.white.withOpacity(0.3),
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                                  color: isSelected ? yellow : (canSelect ? Colors.white : Colors.grey),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        location.name,
                                        style: TextStyle(
                                          color: canSelect ? Colors.white : Colors.grey,
                                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        ),
                                      ),
                                      if (_simController.currentSim.value != null)
                                        Text(
                                          'State: ${_simController.currentSim.value!.states.firstWhere((state) => state.id == location.state).name}',
                                          style: TextStyle(
                                            color: canSelect ? Colors.white70 : Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Colors.white70),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _canProceed()
                      ? () {
                          widget.onLocationsSelected(selectedLocationIds, selectedGridType);
                          Get.back();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: yellow,
                    foregroundColor: Colors.black,
                  ),
                  child: const Text('Start Multi-View'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridTypeOption(int gridType, String label) {
    final isSelected = selectedGridType == gridType;
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onGridTypeChanged(gridType),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isSelected ? yellow.withOpacity(0.2) : Colors.white.withOpacity(0.1),
              border: Border.all(
                color: isSelected ? yellow : Colors.white.withOpacity(0.3),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  color: isSelected ? yellow : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
